<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鐵支調試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .cards-display {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .card {
            width: 60px;
            height: 80px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 14px;
            background: linear-gradient(145deg, #ffffff, #e6e6e6);
            box-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            border: 1px solid #ccc;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 5px 5px 10px rgba(0,0,0,0.4);
        }
        .card.selected {
            background: linear-gradient(145deg, #ffd700, #ffed4e);
            transform: translateY(-10px);
            box-shadow: 0 10px 20px rgba(255, 215, 0, 0.5);
        }
        .card.red { color: #d32f2f; }
        .card.black { color: #333; }
        .card-rank { font-size: 18px; font-weight: bold; }
        .card-suit { font-size: 20px; }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            background: #007bff;
            color: white;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
        }
        .result.success { background: rgba(76, 175, 80, 0.3); }
        .result.error { background: rgba(244, 67, 54, 0.3); }
        .result.info { background: rgba(33, 150, 243, 0.3); }
        h1, h2 { text-align: center; }
        .debug-info {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🃏 鐵支調試頁面</h1>
        
        <div class="section">
            <h2>模擬手牌</h2>
            <p>點擊卡牌來選擇，然後測試鐵支識別：</p>
            <div class="cards-display" id="handCards"></div>
            <div>
                <button onclick="selectFourOfAKind()">自動選擇鐵支</button>
                <button onclick="clearSelection()">清空選擇</button>
                <button onclick="testSelectedCards()" id="testButton">測試選擇的牌</button>
            </div>
        </div>
        
        <div class="section">
            <h2>選擇的牌</h2>
            <div class="cards-display" id="selectedCards"></div>
            <div id="selectionInfo" class="result info">請選擇牌來測試</div>
        </div>
        
        <div class="section">
            <h2>測試結果</h2>
            <div id="testResult"></div>
            <div id="debugInfo" class="debug-info"></div>
        </div>
    </div>
    
    <script src="script.js"></script>
    <script>
        let handCards = [];
        let selectedIndices = [];
        
        // 創建測試手牌（包含鐵支）
        function initializeHand() {
            handCards = [
                new Card('♠', 'A'), new Card('♥', 'A'), new Card('♦', 'A'), new Card('♣', 'A'), // 四張A
                new Card('♠', '3'), new Card('♥', '3'), // 兩張3
                new Card('♠', 'K'), new Card('♥', 'K'), new Card('♦', 'K'), // 三張K
                new Card('♠', '2'), new Card('♥', '2'), // 兩張2
                new Card('♠', 'Q'), new Card('♥', 'J') // 其他牌
            ];
            
            updateHandDisplay();
        }
        
        function updateHandDisplay() {
            const container = document.getElementById('handCards');
            container.innerHTML = '';
            
            handCards.forEach((card, index) => {
                const cardDiv = createCardElement(card, index);
                if (selectedIndices.includes(index)) {
                    cardDiv.classList.add('selected');
                }
                cardDiv.addEventListener('click', () => toggleCardSelection(index));
                container.appendChild(cardDiv);
            });
            
            updateSelectedDisplay();
        }
        
        function createCardElement(card, index) {
            const cardDiv = document.createElement('div');
            cardDiv.className = `card ${card.isRed() ? 'red' : 'black'}`;
            cardDiv.innerHTML = `
                <span class="card-rank">${card.rank}</span>
                <span class="card-suit">${card.suit}</span>
            `;
            cardDiv.dataset.index = index;
            return cardDiv;
        }
        
        function toggleCardSelection(index) {
            const selectedIndex = selectedIndices.indexOf(index);
            if (selectedIndex > -1) {
                selectedIndices.splice(selectedIndex, 1);
            } else {
                selectedIndices.push(index);
            }
            
            updateHandDisplay();
        }
        
        function updateSelectedDisplay() {
            const container = document.getElementById('selectedCards');
            const infoDiv = document.getElementById('selectionInfo');
            
            container.innerHTML = '';
            
            if (selectedIndices.length === 0) {
                infoDiv.textContent = '請選擇牌來測試';
                infoDiv.className = 'result info';
                return;
            }
            
            selectedIndices.forEach(index => {
                const card = handCards[index];
                container.appendChild(createCardElement(card));
            });
            
            infoDiv.textContent = `已選擇 ${selectedIndices.length} 張牌`;
            infoDiv.className = 'result info';
            
            // 更新測試按鈕狀態
            const testButton = document.getElementById('testButton');
            testButton.disabled = selectedIndices.length === 0;
        }
        
        function selectFourOfAKind() {
            // 自動選擇四張A + 一張3
            selectedIndices = [0, 1, 2, 3, 4]; // 四張A + 一張3
            updateHandDisplay();
        }
        
        function clearSelection() {
            selectedIndices = [];
            updateHandDisplay();
        }
        
        function testSelectedCards() {
            if (selectedIndices.length === 0) return;
            
            const selectedCards = selectedIndices.map(index => handCards[index]);
            const resultDiv = document.getElementById('testResult');
            const debugDiv = document.getElementById('debugInfo');
            
            // 清空之前的結果
            resultDiv.innerHTML = '';
            debugDiv.textContent = '';
            
            // 調試信息
            let debugInfo = `選擇的牌數量: ${selectedCards.length}\n`;
            debugInfo += `選擇的牌: ${selectedCards.map(c => c.toString()).join(', ')}\n\n`;
            
            // 創建遊戲實例來測試
            const testGame = new BigTwo();
            
            try {
                const hand = testGame.identifyHand(selectedCards);
                
                debugInfo += `identifyHand 結果: ${hand ? JSON.stringify(hand, null, 2) : 'null'}\n\n`;
                
                if (hand) {
                    if (hand.type === HandType.FOUR_OF_KIND) {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                ✅ 成功識別為鐵支！<br>
                                牌型: ${testGame.getHandTypeName(hand.type)}<br>
                                價值: ${hand.value}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result info">
                                識別為: ${testGame.getHandTypeName(hand.type)}<br>
                                價值: ${hand.value}
                            </div>
                        `;
                    }
                    
                    // 測試是否為有效出牌
                    const isValid = testGame.isValidPlay(hand);
                    debugInfo += `isValidPlay 結果: ${isValid}\n`;
                    
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ 無法識別牌型
                        </div>
                    `;
                }
                
                // 測試各種檢查方法
                if (selectedCards.length === 5) {
                    debugInfo += `\n=== 詳細檢查 ===\n`;
                    debugInfo += `isFourOfAKind: ${testGame.isFourOfAKind(selectedCards)}\n`;
                    debugInfo += `isFullHouse: ${testGame.isFullHouse(selectedCards)}\n`;
                    debugInfo += `isStraight: ${testGame.isStraight(selectedCards)}\n`;
                    debugInfo += `isStraightFlush: ${testGame.isStraightFlush(selectedCards)}\n`;
                    
                    if (testGame.isFourOfAKind(selectedCards)) {
                        debugInfo += `getFourOfAKindValue: ${testGame.getFourOfAKindValue(selectedCards)}\n`;
                    }
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 測試過程中發生錯誤: ${error.message}
                    </div>
                `;
                debugInfo += `錯誤: ${error.message}\n${error.stack}`;
            }
            
            debugDiv.textContent = debugInfo;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeHand();
        });
    </script>
</body>
</html>
