<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鐵支（四條）測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .cards-display {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .card {
            width: 50px;
            height: 70px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 12px;
            background: linear-gradient(145deg, #ffffff, #e6e6e6);
            box-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            border: 1px solid #ccc;
        }
        .card.red { color: #d32f2f; }
        .card.black { color: #333; }
        .card-rank { font-size: 16px; font-weight: bold; }
        .card-suit { font-size: 18px; }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .result.success { background: rgba(76, 175, 80, 0.3); }
        .result.error { background: rgba(244, 67, 54, 0.3); }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            background: #007bff;
            color: white;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        h1, h2 { text-align: center; }
        .test-case {
            border: 1px solid rgba(255,255,255,0.3);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🃏 鐵支（四條）功能測試</h1>
        
        <div class="test-section">
            <h2>測試案例</h2>
            <button onclick="runAllTests()">執行所有測試</button>
            <button onclick="clearResults()">清空結果</button>
            
            <div id="testResults"></div>
        </div>
        
        <div class="test-section">
            <h2>手動測試</h2>
            <p>選擇5張牌來測試鐵支識別：</p>
            <div id="manualTest">
                <div class="cards-display" id="selectedCards"></div>
                <button onclick="addCard('♠', 'A')">♠A</button>
                <button onclick="addCard('♥', 'A')">♥A</button>
                <button onclick="addCard('♦', 'A')">♦A</button>
                <button onclick="addCard('♣', 'A')">♣A</button>
                <button onclick="addCard('♠', '3')">♠3</button>
                <button onclick="clearManualTest()">清空</button>
                <button onclick="testManualCards()">測試這些牌</button>
                <div id="manualResult"></div>
            </div>
        </div>
    </div>
    
    <script>
        const SUITS = ['♣', '♦', '♥', '♠'];
        const RANKS = {
            '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
            'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15
        };
        
        const HandType = {
            SINGLE: 'single',
            PAIR: 'pair',
            THREE: 'three',
            STRAIGHT: 'straight',
            FULL_HOUSE: 'full_house',
            FOUR_OF_KIND: 'four_of_kind',
            STRAIGHT_FLUSH: 'straight_flush'
        };
        
        class Card {
            constructor(suit, rank) {
                this.suit = suit;
                this.rank = rank;
                this.value = this.calculateValue();
            }
            
            calculateValue() {
                const rankValue = RANKS[this.rank];
                const suitValue = SUITS.indexOf(this.suit);
                return rankValue * 4 + suitValue;
            }
            
            isRed() {
                return this.suit === '♥' || this.suit === '♦';
            }
            
            toString() {
                return `${this.suit}${this.rank}`;
            }
        }
        
        // 簡化的遊戲邏輯類別，只包含鐵支相關方法
        class TestGame {
            identifyHand(cards) {
                if (cards.length === 0) return null;
                
                cards.sort((a, b) => a.value - b.value);
                
                if (cards.length === 5) {
                    // 檢查鐵支（四條+一張單牌）
                    if (this.isFourOfAKind(cards)) {
                        return { type: HandType.FOUR_OF_KIND, cards, value: this.getFourOfAKindValue(cards) };
                    }
                    // 檢查葫蘆
                    if (this.isFullHouse(cards)) {
                        return { type: HandType.FULL_HOUSE, cards, value: this.getFullHouseValue(cards) };
                    }
                }
                
                return null;
            }
            
            isFourOfAKind(cards) {
                if (cards.length !== 5) return false;
                
                const ranks = {};
                cards.forEach(card => {
                    ranks[card.rank] = (ranks[card.rank] || 0) + 1;
                });
                
                const counts = Object.values(ranks);
                return counts.includes(4) && counts.includes(1);
            }
            
            getFourOfAKindValue(cards) {
                const ranks = {};
                cards.forEach(card => {
                    ranks[card.rank] = (ranks[card.rank] || 0) + 1;
                });
                
                for (const [rank, count] of Object.entries(ranks)) {
                    if (count === 4) {
                        return RANKS[rank] * 1000;
                    }
                }
                return 0;
            }
            
            isFullHouse(cards) {
                if (cards.length !== 5) return false;
                
                const ranks = {};
                cards.forEach(card => {
                    ranks[card.rank] = (ranks[card.rank] || 0) + 1;
                });
                
                const counts = Object.values(ranks);
                return counts.includes(3) && counts.includes(2);
            }
            
            getFullHouseValue(cards) {
                const ranks = {};
                cards.forEach(card => {
                    ranks[card.rank] = (ranks[card.rank] || 0) + 1;
                });
                
                for (const [rank, count] of Object.entries(ranks)) {
                    if (count === 3) {
                        return RANKS[rank] * 100;
                    }
                }
                return 0;
            }
        }
        
        const testGame = new TestGame();
        let selectedCards = [];
        
        function createCardElement(card) {
            const cardDiv = document.createElement('div');
            cardDiv.className = `card ${card.isRed() ? 'red' : 'black'}`;
            cardDiv.innerHTML = `
                <span class="card-rank">${card.rank}</span>
                <span class="card-suit">${card.suit}</span>
            `;
            return cardDiv;
        }
        
        function displayResult(message, isSuccess) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            return resultDiv;
        }
        
        function runAllTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>測試結果：</h3>';
            
            // 測試案例1：四張A + 一張3
            const testCase1 = [
                new Card('♠', 'A'),
                new Card('♥', 'A'),
                new Card('♦', 'A'),
                new Card('♣', 'A'),
                new Card('♠', '3')
            ];
            
            const testDiv1 = document.createElement('div');
            testDiv1.className = 'test-case';
            testDiv1.innerHTML = '<h4>測試案例1：四張A + 一張3</h4>';
            
            const cardsDiv1 = document.createElement('div');
            cardsDiv1.className = 'cards-display';
            testCase1.forEach(card => cardsDiv1.appendChild(createCardElement(card)));
            testDiv1.appendChild(cardsDiv1);
            
            const result1 = testGame.identifyHand(testCase1);
            if (result1 && result1.type === HandType.FOUR_OF_KIND) {
                testDiv1.appendChild(displayResult(`✅ 成功識別為鐵支，價值：${result1.value}`, true));
            } else {
                testDiv1.appendChild(displayResult(`❌ 識別失敗，結果：${result1 ? result1.type : 'null'}`, false));
            }
            
            resultsDiv.appendChild(testDiv1);
            
            // 測試案例2：四張K + 一張2
            const testCase2 = [
                new Card('♠', 'K'),
                new Card('♥', 'K'),
                new Card('♦', 'K'),
                new Card('♣', 'K'),
                new Card('♥', '2')
            ];
            
            const testDiv2 = document.createElement('div');
            testDiv2.className = 'test-case';
            testDiv2.innerHTML = '<h4>測試案例2：四張K + 一張2</h4>';
            
            const cardsDiv2 = document.createElement('div');
            cardsDiv2.className = 'cards-display';
            testCase2.forEach(card => cardsDiv2.appendChild(createCardElement(card)));
            testDiv2.appendChild(cardsDiv2);
            
            const result2 = testGame.identifyHand(testCase2);
            if (result2 && result2.type === HandType.FOUR_OF_KIND) {
                testDiv2.appendChild(displayResult(`✅ 成功識別為鐵支，價值：${result2.value}`, true));
            } else {
                testDiv2.appendChild(displayResult(`❌ 識別失敗，結果：${result2 ? result2.type : 'null'}`, false));
            }
            
            resultsDiv.appendChild(testDiv2);
            
            // 測試案例3：葫蘆（應該不被識別為鐵支）
            const testCase3 = [
                new Card('♠', 'Q'),
                new Card('♥', 'Q'),
                new Card('♦', 'Q'),
                new Card('♣', 'J'),
                new Card('♠', 'J')
            ];
            
            const testDiv3 = document.createElement('div');
            testDiv3.className = 'test-case';
            testDiv3.innerHTML = '<h4>測試案例3：葫蘆（不應該是鐵支）</h4>';
            
            const cardsDiv3 = document.createElement('div');
            cardsDiv3.className = 'cards-display';
            testCase3.forEach(card => cardsDiv3.appendChild(createCardElement(card)));
            testDiv3.appendChild(cardsDiv3);
            
            const result3 = testGame.identifyHand(testCase3);
            if (result3 && result3.type === HandType.FULL_HOUSE) {
                testDiv3.appendChild(displayResult(`✅ 正確識別為葫蘆，價值：${result3.value}`, true));
            } else {
                testDiv3.appendChild(displayResult(`❌ 識別錯誤，結果：${result3 ? result3.type : 'null'}`, false));
            }
            
            resultsDiv.appendChild(testDiv3);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        function addCard(suit, rank) {
            if (selectedCards.length >= 5) {
                alert('最多只能選擇5張牌');
                return;
            }
            
            selectedCards.push(new Card(suit, rank));
            updateSelectedCardsDisplay();
        }
        
        function clearManualTest() {
            selectedCards = [];
            updateSelectedCardsDisplay();
            document.getElementById('manualResult').innerHTML = '';
        }
        
        function updateSelectedCardsDisplay() {
            const container = document.getElementById('selectedCards');
            container.innerHTML = '';
            selectedCards.forEach(card => container.appendChild(createCardElement(card)));
        }
        
        function testManualCards() {
            const resultDiv = document.getElementById('manualResult');
            
            if (selectedCards.length !== 5) {
                resultDiv.innerHTML = displayResult('請選擇正好5張牌', false).outerHTML;
                return;
            }
            
            const result = testGame.identifyHand([...selectedCards]);
            if (result) {
                if (result.type === HandType.FOUR_OF_KIND) {
                    resultDiv.innerHTML = displayResult(`✅ 識別為鐵支！價值：${result.value}`, true).outerHTML;
                } else {
                    resultDiv.innerHTML = displayResult(`識別為：${result.type}，價值：${result.value}`, true).outerHTML;
                }
            } else {
                resultDiv.innerHTML = displayResult('❌ 無法識別牌型', false).outerHTML;
            }
        }
    </script>
</body>
</html>
