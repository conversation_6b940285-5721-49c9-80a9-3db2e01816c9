<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美味早餐店 - 點餐系統</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍳</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow-x: hidden;
        }

        /* 背景動畫效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            box-shadow:
                0 25px 50px rgba(0,0,0,0.15),
                0 0 0 1px rgba(255,255,255,0.1);
            padding: 60px;
            text-align: center;
            max-width: 700px;
            width: 90%;
            position: relative;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 3em;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background: linear-gradient(45deg, #2c3e50, #3498db);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: #5a6c7d;
            margin-bottom: 50px;
            font-size: 1.3em;
            font-weight: 300;
            line-height: 1.6;
        }

        .version-badge {
            display: inline-block;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 500;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
        }

        .system-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 50px;
        }

        .system-btn {
            padding: 40px 30px;
            border: none;
            border-radius: 20px;
            font-size: 1.4em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-decoration: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .system-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .system-btn:hover::before {
            left: 100%;
        }

        .system-btn .icon {
            font-size: 2.5em;
            margin-bottom: 5px;
        }

        .system-btn .title {
            font-size: 1.2em;
            margin-bottom: 5px;
        }

        .system-btn .desc {
            font-size: 0.9em;
            opacity: 0.9;
            font-weight: 400;
        }

        .pos-btn {
            background: linear-gradient(135deg, #3498db, #2980b9, #1abc9c);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .pos-btn:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(52, 152, 219, 0.4);
        }

        .kds-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b, #e67e22);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
        }

        .kds-btn:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(231, 76, 60, 0.4);
        }

        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            color: #7f8c8d;
            font-weight: 500;
        }

        .features {
            text-align: left;
            background: rgba(248, 249, 250, 0.9);
            backdrop-filter: blur(5px);
            padding: 40px;
            border-radius: 20px;
            margin-top: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .features h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .feature-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
            background: #27ae60;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .status-indicator {
            position: fixed;
            top: 25px;
            right: 25px;
            background: #27ae60;
            color: white;
            padding: 12px 24px;
            border-radius: 30px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .status-indicator::before {
            content: '';
            width: 8px;
            height: 8px;
            background: currentColor;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .status-indicator.offline {
            background: #e74c3c;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .status-indicator.connecting {
            background: #f39c12;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        @media (max-width: 768px) {
            .system-buttons {
                grid-template-columns: 1fr;
            }
            
            .feature-list {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="status-indicator" id="statusIndicator">
        系統連接中...
    </div>

    <div class="container">
        <div class="version-badge">Version 2.0</div>
        <h1>🍳 美味早餐店</h1>
        <p class="subtitle">現代化點餐系統 - 整合 POS 前台與 KDS 廚房顯示</p>

        <div class="stats-section" id="statsSection">
            <div class="stat-card">
                <div class="stat-number" id="todayOrders">-</div>
                <div class="stat-label">今日訂單</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingOrders">-</div>
                <div class="stat-label">待處理</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="completedOrders">-</div>
                <div class="stat-label">已完成</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayRevenue">-</div>
                <div class="stat-label">今日營收</div>
            </div>
        </div>

        <div class="system-buttons">
            <a href="pos.html" class="system-btn pos-btn">
                <div class="icon">📱</div>
                <div class="title">POS 前台點餐</div>
                <div class="desc">店員操作介面 • 點餐結帳</div>
            </a>
            <a href="kds.html" class="system-btn kds-btn">
                <div class="icon">🍽️</div>
                <div class="title">KDS 廚房顯示</div>
                <div class="desc">廚房訂單管理 • 即時更新</div>
            </a>
        </div>

        <div class="features">
            <h3>🚀 系統特色</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <span>即時訂單同步</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <span>觸控友善介面</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎛️</div>
                    <span>客製化選項</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💰</div>
                    <span>自動計算找零</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <span>訂單狀態追蹤</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔔</div>
                    <span>聲音提示功能</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🗄️</div>
                    <span>資料持久化</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📈</div>
                    <span>營收統計分析</span>
                </div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket;
        let statsUpdateInterval;

        // 初始化系統
        function initializeSystem() {
            const indicator = document.getElementById('statusIndicator');
            indicator.textContent = '系統連接中...';
            indicator.classList.add('connecting');

            // 建立 Socket.IO 連接
            socket = io();

            socket.on('connect', function() {
                console.log('✅ 系統連接成功');
                indicator.textContent = '系統已連接';
                indicator.classList.remove('offline', 'connecting');

                // 開始定期更新統計資料
                loadStats();
                statsUpdateInterval = setInterval(loadStats, 30000); // 每30秒更新
            });

            socket.on('disconnect', function() {
                console.log('❌ 系統連接中斷');
                indicator.textContent = '系統已斷線';
                indicator.classList.add('offline');
                indicator.classList.remove('connecting');

                // 停止統計更新
                if (statsUpdateInterval) {
                    clearInterval(statsUpdateInterval);
                }
            });

            socket.on('connect_error', function(error) {
                console.error('🚨 系統連接失敗:', error);
                indicator.textContent = '系統連接失敗';
                indicator.classList.add('offline');
                indicator.classList.remove('connecting');
            });

            socket.on('connected', function(data) {
                console.log('📡 收到伺服器歡迎訊息:', data.message);
            });
        }

        // 載入統計資料
        async function loadStats() {
            try {
                const response = await fetch('/api/orders/stats');
                const result = await response.json();

                if (result.success) {
                    const stats = result.data;
                    updateStatsDisplay(stats);
                } else {
                    console.error('獲取統計失敗:', result.error);
                }
            } catch (error) {
                console.error('載入統計資料失敗:', error);
                // 顯示預設值
                updateStatsDisplay({
                    total: '-',
                    pending: '-',
                    completed: '-',
                    revenue: '-'
                });
            }
        }

        // 更新統計顯示
        function updateStatsDisplay(stats) {
            document.getElementById('todayOrders').textContent = stats.total || '0';
            document.getElementById('pendingOrders').textContent = stats.pending || '0';
            document.getElementById('completedOrders').textContent = stats.completed || '0';

            // 格式化營收顯示
            const revenue = stats.revenue || 0;
            document.getElementById('todayRevenue').textContent =
                revenue > 0 ? `$${revenue}` : '$0';
        }

        // 添加按鈕點擊效果
        function addButtonEffects() {
            const buttons = document.querySelectorAll('.system-btn');

            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // 創建漣漪效果
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255,255,255,0.6)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = (e.clientX - button.offsetLeft) + 'px';
                    ripple.style.top = (e.clientY - button.offsetTop) + 'px';
                    ripple.style.width = ripple.style.height = '20px';

                    button.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        }

        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            addButtonEffects();

            // 添加漣漪動畫樣式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        });

        // 頁面卸載時清理
        window.addEventListener('beforeunload', function() {
            if (statsUpdateInterval) {
                clearInterval(statsUpdateInterval);
            }
            if (socket) {
                socket.disconnect();
            }
        });
    </script>
</body>
</html>