<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>早餐店點餐系統</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 50px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2.5em;
        }

        .subtitle {
            color: #7f8c8d;
            margin-bottom: 40px;
            font-size: 1.2em;
        }

        .system-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .system-btn {
            padding: 30px;
            border: none;
            border-radius: 15px;
            font-size: 1.3em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            color: white;
        }

        .pos-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }

        .pos-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
        }

        .kds-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .kds-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);
        }

        .features {
            text-align: left;
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
        }

        .features h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .feature-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
            background: #27ae60;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
        }

        .status-indicator.offline {
            background: #e74c3c;
        }

        @media (max-width: 768px) {
            .system-buttons {
                grid-template-columns: 1fr;
            }
            
            .feature-list {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="status-indicator" id="statusIndicator">
        系統連接中...
    </div>

    <div class="container">
        <h1>🍳 早餐店點餐系統</h1>
        <p class="subtitle">簡易高效的 POS 與 KDS 整合系統</p>
        
        <div class="system-buttons">
            <a href="pos.html" class="system-btn pos-btn">
                📱 POS 前台點餐<br>
                <small>店員操作介面</small>
            </a>
            <a href="kds.html" class="system-btn kds-btn">
                🍽️ KDS 廚房顯示<br>
                <small>廚房訂單管理</small>
            </a>
        </div>

        <div class="features">
            <h3>系統特色</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>即時訂單同步</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>觸控友善介面</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>客製化選項</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>自動計算找零</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>訂單狀態追蹤</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>聲音提示功能</span>
                </div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        // 檢查系統連接狀態
        function checkSystemStatus() {
            const indicator = document.getElementById('statusIndicator');
            
            // 嘗試連接 Socket.IO
            const socket = io('http://localhost:3001');
            
            socket.on('connect', function() {
                indicator.textContent = '系統已連接';
                indicator.classList.remove('offline');
            });
            
            socket.on('disconnect', function() {
                indicator.textContent = '系統已斷線';
                indicator.classList.add('offline');
            });
            
            socket.on('connect_error', function() {
                indicator.textContent = '系統連接失敗';
                indicator.classList.add('offline');
            });
        }

        // 頁面載入時檢查狀態
        document.addEventListener('DOMContentLoaded', checkSystemStatus);
    </script>
</body>
</html>