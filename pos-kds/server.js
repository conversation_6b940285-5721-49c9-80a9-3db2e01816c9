const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');

// 設定中文時區
moment.locale('zh-tw');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE"]
  }
});

// 中間件設定
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(express.static(__dirname));

// 靜態文件路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

app.get('/pos', (req, res) => {
  res.sendFile(path.join(__dirname, 'pos.html'));
});

app.get('/kds', (req, res) => {
  res.sendFile(path.join(__dirname, 'kds.html'));
});

// 資料庫初始化
const db = new sqlite3.Database('breakfast.db', (err) => {
  if (err) {
    console.error('資料庫連接失敗:', err.message);
  } else {
    console.log('✅ 資料庫連接成功');
  }
});

// 創建資料表
db.serialize(() => {
  // 商品表 - 增加更多欄位
  db.run(`CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    price INTEGER NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    image TEXT,
    available BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // 訂單表 - 增加更多狀態和資訊
  db.run(`CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT NOT NULL UNIQUE,
    order_number TEXT NOT NULL UNIQUE,
    status TEXT DEFAULT 'pending',
    total_amount INTEGER NOT NULL,
    customer_name TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // 訂單項目表 - 優化結構
  db.run(`CREATE TABLE IF NOT EXISTS order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    product_name TEXT NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price INTEGER NOT NULL,
    total_price INTEGER NOT NULL,
    customizations TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products (id)
  )`);

  // 系統設定表
  db.run(`CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT NOT NULL UNIQUE,
    value TEXT NOT NULL,
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  console.log('✅ 資料表創建完成');

  // 初始化商品資料
  initializeProducts();

  // 初始化系統設定
  initializeSettings();
});

// 初始化商品資料函數
function initializeProducts() {
  const products = [
    // 漢堡類
    { name: '豬肉堡', price: 40, category: '漢堡', description: '香煎豬肉排配生菜番茄' },
    { name: '雞肉堡', price: 45, category: '漢堡', description: '嫩煎雞胸肉配特製醬料' },
    { name: '牛肉堡', price: 55, category: '漢堡', description: '厚切牛肉排配洋蔥圈' },
    { name: '卡啦雞腿堡', price: 60, category: '漢堡', description: '酥脆炸雞腿排配美乃滋' },
    { name: '鱈魚堡', price: 50, category: '漢堡', description: '香煎鱈魚排配塔塔醬' },

    // 蛋餅類
    { name: '培根蛋餅', price: 30, category: '蛋餅', description: '香脆培根配蛋餅皮' },
    { name: '豬肉蛋餅', price: 35, category: '蛋餅', description: '香煎豬肉絲配蛋餅' },
    { name: '玉米蛋餅', price: 30, category: '蛋餅', description: '甜玉米粒配蛋餅皮' },
    { name: '火腿蛋餅', price: 35, category: '蛋餅', description: '厚切火腿配蛋餅皮' },
    { name: '起司蛋餅', price: 40, category: '蛋餅', description: '濃郁起司配蛋餅皮' },

    // 吐司類
    { name: '培根吐司', price: 35, category: '吐司', description: '香脆培根夾吐司' },
    { name: '火腿吐司', price: 35, category: '吐司', description: '厚切火腿夾吐司' },
    { name: '鮪魚吐司', price: 40, category: '吐司', description: '鮪魚沙拉夾吐司' },
    { name: '起司吐司', price: 30, category: '吐司', description: '濃郁起司夾吐司' },

    // 飲料類
    { name: '冰紅茶', price: 20, category: '飲料', description: '清香紅茶加冰塊' },
    { name: '冰豆漿', price: 15, category: '飲料', description: '新鮮豆漿加冰塊' },
    { name: '溫奶茶', price: 25, category: '飲料', description: '香濃奶茶溫熱供應' },
    { name: '咖啡', price: 30, category: '飲料', description: '現煮黑咖啡' },
    { name: '冰咖啡', price: 35, category: '飲料', description: '冰涼黑咖啡' },
    { name: '柳橙汁', price: 25, category: '飲料', description: '新鮮柳橙現榨' }
  ];

  const stmt = db.prepare(`
    INSERT OR IGNORE INTO products (name, price, category, description)
    VALUES (?, ?, ?, ?)
  `);

  products.forEach(product => {
    stmt.run(product.name, product.price, product.category, product.description);
  });

  stmt.finalize(() => {
    console.log('✅ 商品資料初始化完成');
  });
}

// 初始化系統設定函數
function initializeSettings() {
  const settings = [
    { key: 'store_name', value: '美味早餐店', description: '店家名稱' },
    { key: 'urgent_time_minutes', value: '10', description: '訂單緊急提醒時間(分鐘)' },
    { key: 'very_urgent_time_minutes', value: '15', description: '訂單非常緊急提醒時間(分鐘)' },
    { key: 'order_counter', value: '1', description: '訂單編號計數器' }
  ];

  const stmt = db.prepare(`
    INSERT OR IGNORE INTO settings (key, value, description)
    VALUES (?, ?, ?)
  `);

  settings.forEach(setting => {
    stmt.run(setting.key, setting.value, setting.description);
  });

  stmt.finalize(() => {
    console.log('✅ 系統設定初始化完成');
  });
}

// ==================== API 路由 ====================

// 獲取所有商品
app.get('/api/products', (req, res) => {
  try {
    db.all(
      'SELECT * FROM products WHERE available = 1 ORDER BY category, name',
      (err, rows) => {
        if (err) {
          console.error('獲取商品失敗:', err);
          return res.status(500).json({
            success: false,
            error: '獲取商品失敗',
            message: err.message
          });
        }

        // 按分類組織商品
        const productsByCategory = rows.reduce((acc, product) => {
          if (!acc[product.category]) {
            acc[product.category] = [];
          }
          acc[product.category].push(product);
          return acc;
        }, {});

        res.json({
          success: true,
          data: {
            products: rows,
            categories: productsByCategory
          }
        });
      }
    );
  } catch (error) {
    console.error('API錯誤:', error);
    res.status(500).json({ success: false, error: '伺服器錯誤' });
  }
});

// 獲取待處理訂單
app.get('/api/orders/pending', (req, res) => {
  try {
    const query = `
      SELECT
        o.id,
        o.uuid,
        o.order_number,
        o.status,
        o.total_amount,
        o.customer_name,
        o.notes,
        o.created_at,
        o.completed_at,
        json_group_array(
          json_object(
            'id', oi.id,
            'product_name', oi.product_name,
            'quantity', oi.quantity,
            'unit_price', oi.unit_price,
            'total_price', oi.total_price,
            'customizations', oi.customizations
          )
        ) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.status = 'pending'
      GROUP BY o.id
      ORDER BY o.created_at ASC
    `;

    db.all(query, (err, rows) => {
      if (err) {
        console.error('獲取待處理訂單失敗:', err);
        return res.status(500).json({
          success: false,
          error: '獲取訂單失敗',
          message: err.message
        });
      }

      const orders = rows.map(row => {
        const items = JSON.parse(row.items).filter(item => item.id !== null);
        return {
          ...row,
          items,
          waitTime: moment().diff(moment(row.created_at), 'minutes'),
          isUrgent: moment().diff(moment(row.created_at), 'minutes') >= 10,
          isVeryUrgent: moment().diff(moment(row.created_at), 'minutes') >= 15
        };
      });

      res.json({
        success: true,
        data: orders
      });
    });
  } catch (error) {
    console.error('API錯誤:', error);
    res.status(500).json({ success: false, error: '伺服器錯誤' });
  }
});

// 創建新訂單
app.post('/api/orders', (req, res) => {
  try {
    const { items, totalAmount, customerName, notes } = req.body;

    // 驗證輸入
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: '訂單項目不能為空'
      });
    }

    if (!totalAmount || totalAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: '訂單金額必須大於0'
      });
    }

    // 生成唯一識別碼和訂單編號
    const orderUuid = uuidv4();

    // 獲取並更新訂單編號計數器
    db.get('SELECT value FROM settings WHERE key = "order_counter"', (err, row) => {
      if (err) {
        console.error('獲取訂單編號失敗:', err);
        return res.status(500).json({ success: false, error: '系統錯誤' });
      }

      const orderCounter = parseInt(row?.value || '1');
      const orderNumber = String(orderCounter).padStart(3, '0');

      // 更新計數器
      db.run(
        'UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = "order_counter"',
        [orderCounter + 1]
      );

      // 創建訂單
      db.run(
        `INSERT INTO orders (uuid, order_number, total_amount, customer_name, notes)
         VALUES (?, ?, ?, ?, ?)`,
        [orderUuid, orderNumber, totalAmount, customerName || null, notes || null],
        function(err) {
          if (err) {
            console.error('創建訂單失敗:', err);
            return res.status(500).json({
              success: false,
              error: '創建訂單失敗',
              message: err.message
            });
          }

          const orderId = this.lastID;

          // 插入訂單項目
          const stmt = db.prepare(`
            INSERT INTO order_items (
              order_id, product_id, product_name, quantity,
              unit_price, total_price, customizations
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `);

          let insertCount = 0;
          const totalItems = items.length;

          items.forEach(item => {
            const itemTotalPrice = item.price * item.quantity;
            stmt.run(
              orderId,
              item.id,
              item.name,
              item.quantity,
              item.price,
              itemTotalPrice,
              JSON.stringify(item.customizations || []),
              (err) => {
                if (err) {
                  console.error('插入訂單項目失敗:', err);
                }
                insertCount++;

                // 所有項目插入完成後
                if (insertCount === totalItems) {
                  stmt.finalize();

                  // 獲取完整的訂單資料
                  getOrderById(orderId, (err, order) => {
                    if (err) {
                      console.error('獲取訂單資料失敗:', err);
                      return res.status(500).json({
                        success: false,
                        error: '獲取訂單資料失敗'
                      });
                    }

                    // 通知 KDS 新訂單
                    io.emit('new_order', order);
                    console.log(`📝 新訂單創建: #${order.order_number}`);

                    res.json({
                      success: true,
                      data: order,
                      message: '訂單創建成功'
                    });
                  });
                }
              }
            );
          });
        }
      );
    });
  } catch (error) {
    console.error('創建訂單API錯誤:', error);
    res.status(500).json({ success: false, error: '伺服器錯誤' });
  }
});

// 輔助函數：根據ID獲取訂單詳情
function getOrderById(orderId, callback) {
  const query = `
    SELECT
      o.id,
      o.uuid,
      o.order_number,
      o.status,
      o.total_amount,
      o.customer_name,
      o.notes,
      o.created_at,
      o.completed_at,
      json_group_array(
        json_object(
          'id', oi.id,
          'product_name', oi.product_name,
          'quantity', oi.quantity,
          'unit_price', oi.unit_price,
          'total_price', oi.total_price,
          'customizations', oi.customizations
        )
      ) as items
    FROM orders o
    LEFT JOIN order_items oi ON o.id = oi.order_id
    WHERE o.id = ?
    GROUP BY o.id
  `;

  db.get(query, [orderId], (err, row) => {
    if (err) {
      return callback(err, null);
    }

    if (!row) {
      return callback(new Error('訂單不存在'), null);
    }

    const order = {
      ...row,
      items: JSON.parse(row.items).filter(item => item.id !== null),
      waitTime: moment().diff(moment(row.created_at), 'minutes'),
      isUrgent: moment().diff(moment(row.created_at), 'minutes') >= 10,
      isVeryUrgent: moment().diff(moment(row.created_at), 'minutes') >= 15
    };

    callback(null, order);
  });
}

// 完成訂單
app.put('/api/orders/:id/complete', (req, res) => {
  try {
    const orderId = req.params.id;

    if (!orderId || isNaN(orderId)) {
      return res.status(400).json({
        success: false,
        error: '無效的訂單ID'
      });
    }

    db.run(
      `UPDATE orders SET
       status = 'completed',
       completed_at = CURRENT_TIMESTAMP,
       updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [orderId],
      function(err) {
        if (err) {
          console.error('完成訂單失敗:', err);
          return res.status(500).json({
            success: false,
            error: '完成訂單失敗',
            message: err.message
          });
        }

        if (this.changes === 0) {
          return res.status(404).json({
            success: false,
            error: '訂單不存在'
          });
        }

        // 獲取訂單資訊用於通知
        getOrderById(orderId, (err, order) => {
          if (!err && order) {
            // 通知所有客戶端訂單已完成
            io.emit('order_completed', {
              orderId: parseInt(orderId),
              orderNumber: order.order_number,
              completedAt: moment().format('YYYY-MM-DD HH:mm:ss')
            });
            console.log(`✅ 訂單完成: #${order.order_number}`);
          }
        });

        res.json({
          success: true,
          message: '訂單已完成',
          data: { orderId: parseInt(orderId) }
        });
      }
    );
  } catch (error) {
    console.error('完成訂單API錯誤:', error);
    res.status(500).json({ success: false, error: '伺服器錯誤' });
  }
});

// 獲取今日訂單統計
app.get('/api/orders/stats', (req, res) => {
  try {
    const today = moment().format('YYYY-MM-DD');

    const queries = {
      total: `SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = ?`,
      completed: `SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = ? AND status = 'completed'`,
      pending: `SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = ? AND status = 'pending'`,
      revenue: `SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE DATE(created_at) = ? AND status = 'completed'`
    };

    const stats = {};
    let completedQueries = 0;
    const totalQueries = Object.keys(queries).length;

    Object.entries(queries).forEach(([key, query]) => {
      db.get(query, [today], (err, row) => {
        if (err) {
          console.error(`獲取${key}統計失敗:`, err);
          stats[key] = 0;
        } else {
          stats[key] = key === 'revenue' ? row.total : row.count;
        }

        completedQueries++;
        if (completedQueries === totalQueries) {
          res.json({
            success: true,
            data: {
              date: today,
              ...stats
            }
          });
        }
      });
    });
  } catch (error) {
    console.error('獲取統計API錯誤:', error);
    res.status(500).json({ success: false, error: '伺服器錯誤' });
  }
});

// 獲取系統設定
app.get('/api/settings', (req, res) => {
  try {
    db.all('SELECT * FROM settings', (err, rows) => {
      if (err) {
        console.error('獲取設定失敗:', err);
        return res.status(500).json({ success: false, error: '獲取設定失敗' });
      }

      const settings = rows.reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {});

      res.json({
        success: true,
        data: settings
      });
    });
  } catch (error) {
    console.error('獲取設定API錯誤:', error);
    res.status(500).json({ success: false, error: '伺服器錯誤' });
  }
});

// ==================== WebSocket 處理 ====================
io.on('connection', (socket) => {
  console.log(`🔗 客戶端已連接: ${socket.id}`);

  // 發送歡迎訊息
  socket.emit('connected', {
    message: '連接成功',
    timestamp: moment().format('YYYY-MM-DD HH:mm:ss')
  });

  // 處理 POS 系統事件
  socket.on('pos_ready', () => {
    console.log(`📱 POS 系統就緒: ${socket.id}`);
    socket.join('pos');
  });

  // 處理 KDS 系統事件
  socket.on('kds_ready', () => {
    console.log(`🍽️ KDS 系統就緒: ${socket.id}`);
    socket.join('kds');

    // 發送當前待處理訂單
    db.all(`
      SELECT
        o.id, o.uuid, o.order_number, o.status, o.total_amount,
        o.customer_name, o.notes, o.created_at,
        json_group_array(
          json_object(
            'id', oi.id,
            'product_name', oi.product_name,
            'quantity', oi.quantity,
            'unit_price', oi.unit_price,
            'total_price', oi.total_price,
            'customizations', oi.customizations
          )
        ) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.status = 'pending'
      GROUP BY o.id
      ORDER BY o.created_at ASC
    `, (err, rows) => {
      if (!err) {
        const orders = rows.map(row => ({
          ...row,
          items: JSON.parse(row.items).filter(item => item.id !== null),
          waitTime: moment().diff(moment(row.created_at), 'minutes'),
          isUrgent: moment().diff(moment(row.created_at), 'minutes') >= 10,
          isVeryUrgent: moment().diff(moment(row.created_at), 'minutes') >= 15
        }));

        socket.emit('pending_orders', orders);
      }
    });
  });

  // 處理心跳檢測
  socket.on('ping', () => {
    socket.emit('pong', { timestamp: moment().format('YYYY-MM-DD HH:mm:ss') });
  });

  // 處理斷線
  socket.on('disconnect', (reason) => {
    console.log(`❌ 客戶端已斷線: ${socket.id}, 原因: ${reason}`);
  });

  // 處理錯誤
  socket.on('error', (error) => {
    console.error(`🚨 Socket錯誤 ${socket.id}:`, error);
  });
});

// ==================== 伺服器啟動 ====================
const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log('🚀 ==========================================');
  console.log(`🍳 早餐店點餐系統已啟動`);
  console.log(`📡 伺服器運行在: http://localhost:${PORT}`);
  console.log(`📱 POS 系統: http://localhost:${PORT}/pos`);
  console.log(`🍽️ KDS 系統: http://localhost:${PORT}/kds`);
  console.log('🚀 ==========================================');
});

// 優雅關閉處理
process.on('SIGINT', () => {
  console.log('\n🛑 正在關閉伺服器...');

  db.close((err) => {
    if (err) {
      console.error('關閉資料庫失敗:', err.message);
    } else {
      console.log('✅ 資料庫連接已關閉');
    }
  });

  server.close(() => {
    console.log('✅ 伺服器已關閉');
    process.exit(0);
  });
});

// 未捕獲的異常處理
process.on('uncaughtException', (err) => {
  console.error('🚨 未捕獲的異常:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 未處理的Promise拒絕:', reason);
  console.error('Promise:', promise);
});