const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 初始化資料庫
const db = new sqlite3.Database('breakfast.db');

// 創建資料表
db.serialize(() => {
  // 商品表
  db.run(`CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    price INTEGER NOT NULL,
    category TEXT NOT NULL,
    image TEXT
  )`);

  // 訂單表
  db.run(`CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    total_amount INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME
  )`);

  // 訂單項目表
  db.run(`CREATE TABLE IF NOT EXISTS order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER,
    product_id INTEGER,
    product_name TEXT,
    quantity INTEGER,
    price INTEGER,
    customizations TEXT,
    FOREIGN KEY (order_id) REFERENCES orders (id),
    FOREIGN KEY (product_id) REFERENCES products (id)
  )`);

  // 初始化商品資料
  const products = [
    { name: '豬肉堡', price: 40, category: '漢堡' },
    { name: '雞肉堡', price: 45, category: '漢堡' },
    { name: '牛肉堡', price: 55, category: '漢堡' },
    { name: '卡啦雞腿堡', price: 60, category: '漢堡' },
    { name: '鱈魚堡', price: 50, category: '漢堡' },
    { name: '培根蛋餅', price: 30, category: '蛋餅' },
    { name: '豬肉蛋餅', price: 35, category: '蛋餅' },
    { name: '玉米蛋餅', price: 30, category: '蛋餅' },
    { name: '火腿蛋餅', price: 35, category: '蛋餅' },
    { name: '培根吐司', price: 35, category: '吐司' },
    { name: '火腿吐司', price: 35, category: '吐司' },
    { name: '鮪魚吐司', price: 40, category: '吐司' },
    { name: '冰紅茶', price: 20, category: '飲料' },
    { name: '冰豆漿', price: 15, category: '飲料' },
    { name: '溫奶茶', price: 25, category: '飲料' },
    { name: '咖啡', price: 30, category: '飲料' }
  ];

  const stmt = db.prepare('INSERT OR IGNORE INTO products (name, price, category) VALUES (?, ?, ?)');
  products.forEach(product => {
    stmt.run(product.name, product.price, product.category);
  });
  stmt.finalize();
});

// API 路由
// 獲取所有商品
app.get('/api/products', (req, res) => {
  db.all('SELECT * FROM products ORDER BY category, name', (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json(rows);
  });
});

// 獲取待處理訂單
app.get('/api/orders/pending', (req, res) => {
  const query = `
    SELECT o.*, 
           json_group_array(
             json_object(
               'id', oi.id,
               'product_name', oi.product_name,
               'quantity', oi.quantity,
               'price', oi.price,
               'customizations', oi.customizations
             )
           ) as items
    FROM orders o
    LEFT JOIN order_items oi ON o.id = oi.order_id
    WHERE o.status = 'pending'
    GROUP BY o.id
    ORDER BY o.created_at ASC
  `;
  
  db.all(query, (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    
    const orders = rows.map(row => ({
      ...row,
      items: JSON.parse(row.items).filter(item => item.id !== null)
    }));
    
    res.json(orders);
  });
});

// 創建新訂單
app.post('/api/orders', (req, res) => {
  const { items, totalAmount } = req.body;
  
  // 生成訂單編號
  const orderNumber = String(Date.now()).slice(-3).padStart(3, '0');
  
  db.run(
    'INSERT INTO orders (order_number, total_amount) VALUES (?, ?)',
    [orderNumber, totalAmount],
    function(err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      
      const orderId = this.lastID;
      
      // 插入訂單項目
      const stmt = db.prepare(`
        INSERT INTO order_items (order_id, product_id, product_name, quantity, price, customizations)
        VALUES (?, ?, ?, ?, ?, ?)
      `);
      
      items.forEach(item => {
        stmt.run(
          orderId,
          item.id,
          item.name,
          item.quantity,
          item.price,
          JSON.stringify(item.customizations || [])
        );
      });
      
      stmt.finalize();
      
      // 獲取完整的訂單資料
      const query = `
        SELECT o.*, 
               json_group_array(
                 json_object(
                   'id', oi.id,
                   'product_name', oi.product_name,
                   'quantity', oi.quantity,
                   'price', oi.price,
                   'customizations', oi.customizations
                 )
               ) as items
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.id = ?
        GROUP BY o.id
      `;
      
      db.get(query, [orderId], (err, row) => {
        if (err) {
          res.status(500).json({ error: err.message });
          return;
        }
        
        const order = {
          ...row,
          items: JSON.parse(row.items).filter(item => item.id !== null)
        };
        
        // 通知 KDS 新訂單
        io.emit('new_order', order);
        
        res.json(order);
      });
    }
  );
});

// 完成訂單
app.put('/api/orders/:id/complete', (req, res) => {
  const orderId = req.params.id;
  
  db.run(
    'UPDATE orders SET status = "completed", completed_at = CURRENT_TIMESTAMP WHERE id = ?',
    [orderId],
    function(err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      
      // 通知所有客戶端訂單已完成
      io.emit('order_completed', { orderId: parseInt(orderId) });
      
      res.json({ message: '訂單已完成' });
    }
  );
});

// WebSocket 連接處理
io.on('connection', (socket) => {
  console.log('客戶端已連接:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('客戶端已斷線:', socket.id);
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`服務器運行在 http://localhost:${PORT}`);
});