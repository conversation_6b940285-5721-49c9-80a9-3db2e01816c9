{"name": "breakfast-pos-kds-system", "version": "2.0.0", "description": "現代化早餐店點餐系統 - 整合 POS 前台與 KDS 廚房顯示系統", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js --watch server.js --watch public", "init": "node scripts/init-db.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "moment": "^2.30.1", "socket.io": "^4.7.2", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["pos", "kds", "restaurant", "breakfast", "order-system", "point-of-sale", "kitchen-display", "real-time", "websocket"], "author": "Breakfast POS Team", "license": "MIT", "engines": {"node": ">=14.0.0"}}