const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 啟動早餐店點餐系統...\n');

// 啟動後端服務器
const server = spawn('node', ['server.js'], {
  stdio: 'inherit',
  cwd: __dirname
});

server.on('error', (err) => {
  console.error('啟動服務器失敗:', err);
});

server.on('close', (code) => {
  console.log(`服務器已關閉，退出碼: ${code}`);
});

// 優雅關閉
process.on('SIGINT', () => {
  console.log('\n正在關閉系統...');
  server.kill('SIGINT');
  process.exit(0);
});

console.log('系統啟動完成！');
console.log('📱 POS 系統: http://localhost:3001/pos.html');
console.log('🍽️  KDS 系統: http://localhost:3001/kds.html');
console.log('🏠 主頁面: http://localhost:3001');