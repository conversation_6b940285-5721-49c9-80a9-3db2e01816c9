const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 ==========================================');
console.log('🍳 美味早餐店點餐系統 v2.0');
console.log('🚀 ==========================================\n');

// 檢查必要文件
function checkRequiredFiles() {
  const requiredFiles = [
    'server.js',
    'package.json',
    'index.html',
    'pos.html',
    'kds.html'
  ];

  const missingFiles = requiredFiles.filter(file => !fs.existsSync(path.join(__dirname, file)));

  if (missingFiles.length > 0) {
    console.error('❌ 缺少必要文件:', missingFiles.join(', '));
    process.exit(1);
  }

  console.log('✅ 系統文件檢查完成');
}

// 檢查依賴項
function checkDependencies() {
  try {
    const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
    const nodeModulesExists = fs.existsSync(path.join(__dirname, 'node_modules'));

    if (!nodeModulesExists) {
      console.log('⚠️  未找到 node_modules，請先執行 npm install');
      process.exit(1);
    }

    console.log('✅ 依賴項檢查完成');
    console.log(`📦 系統版本: ${packageJson.version}`);
  } catch (error) {
    console.error('❌ 檢查依賴項失敗:', error.message);
    process.exit(1);
  }
}

// 啟動前檢查
console.log('🔍 執行啟動前檢查...');
checkRequiredFiles();
checkDependencies();

console.log('\n🚀 啟動服務器...');

// 啟動後端服務器
const server = spawn('node', ['server.js'], {
  stdio: 'inherit',
  cwd: __dirname,
  env: { ...process.env, NODE_ENV: 'production' }
});

server.on('error', (err) => {
  console.error('❌ 啟動服務器失敗:', err);
  process.exit(1);
});

server.on('close', (code) => {
  if (code === 0) {
    console.log('✅ 服務器正常關閉');
  } else {
    console.log(`❌ 服務器異常關閉，退出碼: ${code}`);
  }
});

// 優雅關閉處理
process.on('SIGINT', () => {
  console.log('\n🛑 正在關閉系統...');
  server.kill('SIGINT');

  setTimeout(() => {
    console.log('⏰ 強制關閉系統');
    process.exit(1);
  }, 5000);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到終止信號，正在關閉系統...');
  server.kill('SIGTERM');
});

// 未捕獲異常處理
process.on('uncaughtException', (err) => {
  console.error('🚨 未捕獲的異常:', err);
  server.kill('SIGKILL');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 未處理的Promise拒絕:', reason);
  console.error('Promise:', promise);
});

// 顯示系統資訊
setTimeout(() => {
  console.log('\n🎉 ==========================================');
  console.log('✅ 早餐店點餐系統啟動成功！');
  console.log('🎉 ==========================================');
  console.log('🌐 系統訪問地址:');
  console.log('🏠 主頁面:     http://localhost:3001');
  console.log('📱 POS 系統:   http://localhost:3001/pos');
  console.log('🍽️  KDS 系統:   http://localhost:3001/kds');
  console.log('🎉 ==========================================');
  console.log('💡 提示: 按 Ctrl+C 可安全關閉系統');
  console.log('📚 系統功能:');
  console.log('   • 即時訂單同步');
  console.log('   • 觸控友善介面');
  console.log('   • 客製化選項');
  console.log('   • 自動計算找零');
  console.log('   • 訂單狀態追蹤');
  console.log('   • 聲音提示功能');
  console.log('   • 營收統計分析');
  console.log('🎉 ==========================================\n');
}, 2000);