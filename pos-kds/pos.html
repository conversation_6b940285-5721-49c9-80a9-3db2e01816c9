<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美味早餐店 - POS 點餐系統</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📱</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .pos-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            height: 100vh;
            gap: 2px;
        }

        /* 左側菜單區域 */
        .menu-section {
            background: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .menu-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .store-info h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .store-info .subtitle {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .datetime {
            text-align: right;
            font-size: 0.9em;
        }

        .category-tabs {
            display: flex;
            background: #34495e;
            border-bottom: 3px solid #3498db;
        }

        .category-tab {
            flex: 1;
            padding: 15px 10px;
            background: #34495e;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .category-tab:hover {
            background: #2c3e50;
        }

        .category-tab.active {
            background: #3498db;
            color: white;
        }

        .category-tab.active::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            right: 0;
            height: 3px;
            background: #2980b9;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            padding: 20px;
            height: calc(100vh - 140px);
            overflow-y: auto;
        }

        .menu-item {
            background: white;
            border: 2px solid #ecf0f1;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .menu-item:hover {
            border-color: #3498db;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.2);
        }

        .menu-item h3 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .menu-item .description {
            font-size: 0.85em;
            color: #7f8c8d;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .menu-item .price {
            font-size: 1.3em;
            font-weight: 700;
            color: #e74c3c;
            background: #ffeaa7;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
        }

        /* 右側訂單區域 */
        .order-section {
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .order-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .order-number {
            font-size: 1.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .order-time {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .order-items {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: calc(100vh - 300px);
        }

        .order-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .item-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .item-price {
            font-weight: 700;
            color: #e74c3c;
        }

        .item-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            background: #2980b9;
            transform: scale(1.1);
        }

        .quantity {
            font-weight: 600;
            min-width: 30px;
            text-align: center;
        }

        .remove-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .order-summary {
            background: white;
            padding: 20px;
            border-top: 2px solid #ecf0f1;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .total-row {
            font-size: 1.3em;
            font-weight: 700;
            color: #2c3e50;
            border-top: 2px solid #3498db;
            padding-top: 10px;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-clear {
            background: #95a5a6;
            color: white;
        }

        .btn-send {
            background: #27ae60;
            color: white;
        }

        .btn-checkout {
            background: #e74c3c;
            color: white;
            grid-column: 1 / -1;
            margin-top: 10px;
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .empty-cart {
            text-align: center;
            padding: 40px 20px;
            color: #7f8c8d;
        }

        .empty-cart .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="pos-container">
        <!-- 左側菜單區域 -->
        <div class="menu-section">
            <div class="menu-header">
                <div class="store-info">
                    <h1>🍳 美味早餐店</h1>
                    <div class="subtitle">POS 點餐系統</div>
                </div>
                <div class="datetime" id="datetime"></div>
            </div>
            
            <div class="category-tabs" id="categoryTabs">
                <!-- 分類標籤將由 JavaScript 動態生成 -->
            </div>
            
            <div class="menu-grid" id="menuGrid">
                <!-- 菜單項目將由 JavaScript 動態生成 -->
            </div>
        </div>

        <!-- 右側訂單區域 -->
        <div class="order-section">
            <div class="order-header">
                <div class="order-number">訂單 #<span id="orderNumber">---</span></div>
                <div class="order-time" id="orderTime"></div>
            </div>
            
            <div class="order-items" id="orderItems">
                <div class="empty-cart">
                    <div class="icon">🛒</div>
                    <div>尚未選擇任何商品</div>
                </div>
            </div>
            
            <div class="order-summary">
                <div class="summary-row">
                    <span>項目數量:</span>
                    <span id="itemCount">0</span>
                </div>
                <div class="summary-row total-row">
                    <span>總計:</span>
                    <span id="totalAmount">$0</span>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-clear" id="clearBtn">清除</button>
                    <button class="btn btn-send" id="sendBtn" disabled>送單</button>
                    <button class="btn btn-checkout" id="checkoutBtn" disabled>結帳</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        // 全域變數
        let socket;
        let products = [];
        let categories = {};
        let currentCategory = '';
        let cart = [];
        let orderStartTime = new Date();

        // 初始化系統
        function initializePOS() {
            console.log('🚀 初始化 POS 系統...');
            
            // 建立 Socket.IO 連接
            socket = io();
            
            socket.on('connect', function() {
                console.log('✅ POS 系統連接成功');
                socket.emit('pos_ready');
            });
            
            socket.on('disconnect', function() {
                console.log('❌ POS 系統連接中斷');
            });
            
            // 載入商品資料
            loadProducts();
            
            // 更新時間顯示
            updateDateTime();
            setInterval(updateDateTime, 1000);
            
            // 綁定事件
            bindEvents();
        }

        // 載入商品資料
        async function loadProducts() {
            try {
                const response = await fetch('/api/products');
                const result = await response.json();
                
                if (result.success) {
                    products = result.data.products;
                    categories = result.data.categories;
                    
                    renderCategories();
                    
                    // 預設顯示第一個分類
                    const firstCategory = Object.keys(categories)[0];
                    if (firstCategory) {
                        switchCategory(firstCategory);
                    }
                } else {
                    console.error('載入商品失敗:', result.error);
                }
            } catch (error) {
                console.error('載入商品資料失敗:', error);
            }
        }

        // 渲染分類標籤
        function renderCategories() {
            const tabsContainer = document.getElementById('categoryTabs');
            tabsContainer.innerHTML = '';
            
            Object.keys(categories).forEach(category => {
                const tab = document.createElement('button');
                tab.className = 'category-tab';
                tab.textContent = category;
                tab.onclick = () => switchCategory(category);
                tabsContainer.appendChild(tab);
            });
        }

        // 切換分類
        function switchCategory(category) {
            currentCategory = category;
            
            // 更新標籤狀態
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.toggle('active', tab.textContent === category);
            });
            
            // 渲染該分類的商品
            renderProducts(categories[category]);
        }

        // 渲染商品
        function renderProducts(categoryProducts) {
            const menuGrid = document.getElementById('menuGrid');
            menuGrid.innerHTML = '';
            
            categoryProducts.forEach(product => {
                const item = document.createElement('div');
                item.className = 'menu-item';
                item.innerHTML = `
                    <h3>${product.name}</h3>
                    <div class="description">${product.description || ''}</div>
                    <div class="price">$${product.price}</div>
                `;
                item.onclick = () => addToCart(product);
                menuGrid.appendChild(item);
            });
        }

        // 添加到購物車
        function addToCart(product) {
            const existingItem = cart.find(item => item.id === product.id);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    ...product,
                    quantity: 1,
                    customizations: []
                });
            }
            
            renderCart();
            updateSummary();
        }

        // 渲染購物車
        function renderCart() {
            const orderItems = document.getElementById('orderItems');
            
            if (cart.length === 0) {
                orderItems.innerHTML = `
                    <div class="empty-cart">
                        <div class="icon">🛒</div>
                        <div>尚未選擇任何商品</div>
                    </div>
                `;
                return;
            }
            
            orderItems.innerHTML = cart.map(item => `
                <div class="order-item">
                    <div class="item-header">
                        <span class="item-name">${item.name}</span>
                        <span class="item-price">$${item.price * item.quantity}</span>
                    </div>
                    <div class="item-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, -1)">-</button>
                        <span class="quantity">${item.quantity}</span>
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, 1)">+</button>
                        <button class="remove-btn" onclick="removeFromCart(${item.id})">移除</button>
                    </div>
                </div>
            `).join('');
        }

        // 更新數量
        function updateQuantity(productId, change) {
            const item = cart.find(item => item.id === productId);
            if (item) {
                item.quantity += change;
                if (item.quantity <= 0) {
                    removeFromCart(productId);
                } else {
                    renderCart();
                    updateSummary();
                }
            }
        }

        // 從購物車移除
        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            renderCart();
            updateSummary();
        }

        // 更新摘要
        function updateSummary() {
            const itemCount = cart.reduce((sum, item) => sum + item.quantity, 0);
            const totalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
            document.getElementById('itemCount').textContent = itemCount;
            document.getElementById('totalAmount').textContent = `$${totalAmount}`;
            
            // 更新按鈕狀態
            const hasItems = cart.length > 0;
            document.getElementById('sendBtn').disabled = !hasItems;
            document.getElementById('checkoutBtn').disabled = !hasItems;
        }

        // 更新時間顯示
        function updateDateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('datetime').textContent = timeString;
            document.getElementById('orderTime').textContent = `開始時間: ${orderStartTime.toLocaleTimeString('zh-TW')}`;
        }

        // 綁定事件
        function bindEvents() {
            document.getElementById('clearBtn').onclick = clearCart;
            document.getElementById('sendBtn').onclick = sendOrder;
            document.getElementById('checkoutBtn').onclick = checkout;
        }

        // 清除購物車
        function clearCart() {
            if (confirm('確定要清除所有商品嗎？')) {
                cart = [];
                renderCart();
                updateSummary();
                orderStartTime = new Date();
            }
        }

        // 送單
        async function sendOrder() {
            if (cart.length === 0) return;
            
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.innerHTML = '送單中...';
            
            try {
                const orderData = {
                    items: cart,
                    totalAmount: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)
                };
                
                const response = await fetch('/api/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(`訂單已送出！訂單編號: #${result.data.order_number}`);
                    document.getElementById('orderNumber').textContent = result.data.order_number;
                    
                    // 清除購物車
                    cart = [];
                    renderCart();
                    updateSummary();
                    orderStartTime = new Date();
                } else {
                    alert('送單失敗: ' + result.error);
                }
            } catch (error) {
                console.error('送單失敗:', error);
                alert('送單失敗，請稍後再試');
            } finally {
                sendBtn.disabled = false;
                sendBtn.innerHTML = '送單';
            }
        }

        // 結帳
        function checkout() {
            if (cart.length === 0) return;
            
            const totalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const received = prompt(`總金額: $${totalAmount}\n請輸入收到的金額:`);
            
            if (received !== null) {
                const receivedAmount = parseInt(received);
                if (receivedAmount >= totalAmount) {
                    const change = receivedAmount - totalAmount;
                    alert(`結帳完成！\n總金額: $${totalAmount}\n收款: $${receivedAmount}\n找零: $${change}`);
                    
                    // 清除購物車
                    cart = [];
                    renderCart();
                    updateSummary();
                    orderStartTime = new Date();
                } else {
                    alert('金額不足！');
                }
            }
        }

        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', initializePOS);
    </script>
</body>
</html>
