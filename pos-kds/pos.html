<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美味早餐店 - POS 點餐系統</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📱</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .pos-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            height: 100vh;
            gap: 2px;
        }

        /* 左側菜單區域 */
        .menu-section {
            background: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .menu-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .store-info h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .store-info .subtitle {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .datetime {
            text-align: right;
            font-size: 0.9em;
        }

        .category-tabs {
            display: flex;
            background: #34495e;
            border-bottom: 3px solid #3498db;
        }

        .category-tab {
            flex: 1;
            padding: 15px 10px;
            background: #34495e;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .category-tab:hover {
            background: #2c3e50;
        }

        .category-tab.active {
            background: #3498db;
            color: white;
        }

        .category-tab.active::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            right: 0;
            height: 3px;
            background: #2980b9;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            padding: 20px;
            height: calc(100vh - 140px);
            overflow-y: auto;
        }

        .menu-item {
            background: white;
            border: 2px solid #ecf0f1;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.5s;
        }

        .menu-item:hover::before {
            left: 100%;
        }

        .menu-item:hover {
            border-color: #3498db;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.2);
        }

        .menu-item.unavailable {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .menu-item h3 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .menu-item .description {
            font-size: 0.85em;
            color: #7f8c8d;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .menu-item .price {
            font-size: 1.3em;
            font-weight: 700;
            color: #e74c3c;
            background: #ffeaa7;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
        }

        /* 右側訂單區域 */
        .order-section {
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .order-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .order-number {
            font-size: 1.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .order-time {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>早餐店 POS 系統</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .pos-container {
            display: flex;
            height: 100vh;
        }

        .menu-section {
            flex: 2;
            background: white;
            border-right: 2px solid #ddd;
        }

        .order-section {
            flex: 1;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }

        .category-tabs {
            display: flex;
            background: #2c3e50;
            padding: 0;
        }

        .category-tab {
            flex: 1;
            padding: 15px;
            background: #34495e;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .category-tab:hover {
            background: #2c3e50;
        }

        .category-tab.active {
            background: #3498db;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 15px;
            padding: 20px;
            height: calc(100vh - 60px);
            overflow-y: auto;
        }

        .menu-item {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .menu-item:hover {
            border-color: #3498db;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .menu-item h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .menu-item .price {
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
        }

        .order-header {
            background: #2c3e50;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }

        .order-items {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .order-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .order-item-info {
            flex: 1;
        }

        .order-item-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .order-item-customizations {
            font-size: 12px;
            color: #666;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            background: #3498db;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
        }

        .quantity-btn:hover {
            background: #2980b9;
        }

        .remove-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }

        .remove-btn:hover {
            background: #c0392b;
        }

        .order-total {
            background: #34495e;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            padding: 20px;
        }

        .action-btn {
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .clear-btn {
            background: #95a5a6;
            color: white;
        }

        .clear-btn:hover {
            background: #7f8c8d;
        }

        .send-btn {
            background: #27ae60;
            color: white;
        }

        .send-btn:hover {
            background: #229954;
        }

        .checkout-btn {
            background: #f39c12;
            color: white;
            grid-column: 1 / -1;
        }

                .checkout-btn:hover {
            background: #e67e22;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 10px;
            width: 400px;
            text-align: center;
        }

        .modal h2 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .modal input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .modal-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .modal-cancel {
            background: #95a5a6;
            color: white;
        }

        .modal-confirm {
            background: #27ae60;
            color: white;
        }

        .customization-modal .modal-content {
            width: 500px;
        }

        .customization-options {
            text-align: left;
            margin: 20px 0;
        }

        .customization-option {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }

        .customization-option input {
            margin-right: 10px;
            width: auto;
        }

        .empty-cart {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <div class="pos-container">
        <!-- 菜單區域 -->
        <div class="menu-section">
            <div class="category-tabs" id="categoryTabs">
                <!-- 動態生成分類標籤 -->
            </div>
            <div class="menu-grid" id="menuGrid">
                <!-- 動態生成菜單項目 -->
            </div>
        </div>

        <!-- 訂單區域 -->
        <div class="order-section">
            <div class="order-header">
                訂單明細
            </div>
            <div class="order-items" id="orderItems">
                <div class="empty-cart">尚無點餐項目</div>
            </div>
            <div class="order-total" id="orderTotal">
                總計: $0
            </div>
            <div class="action-buttons">
                <button class="action-btn clear-btn" onclick="clearOrder()">清除訂單</button>
                <button class="action-btn send-btn" onclick="sendOrder()">送單至廚房</button>
                <button class="action-btn checkout-btn" onclick="showCheckoutModal()">結帳</button>
            </div>
        </div>
    </div>

    <!-- 客製化選項彈窗 -->
    <div id="customizationModal" class="modal customization-modal">
        <div class="modal-content">
            <h2>客製化選項</h2>
            <div id="customizationOptions" class="customization-options">
                <!-- 動態生成客製化選項 -->
            </div>
            <div class="modal-buttons">
                <button class="modal-btn modal-cancel" onclick="closeCustomizationModal()">取消</button>
                <button class="modal-btn modal-confirm" onclick="confirmCustomization()">確定</button>
            </div>
        </div>
    </div>

    <!-- 結帳彈窗 -->
    <div id="checkoutModal" class="modal">
        <div class="modal-content">
            <h2>結帳</h2>
            <div style="font-size: 18px; margin: 20px 0;">
                應收金額: $<span id="checkoutAmount">0</span>
            </div>
            <input type="number" id="receivedAmount" placeholder="實收金額" onchange="calculateChange()">
            <div style="font-size: 18px; margin: 20px 0;">
                找零: $<span id="changeAmount">0</span>
            </div>
            <div class="modal-buttons">
                <button class="modal-btn modal-cancel" onclick="closeCheckoutModal()">取消</button>
                <button class="modal-btn modal-confirm" onclick="completeCheckout()">完成結帳</button>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        // 全域變數
        let products = [];
        let currentOrder = [];
        let currentCategory = '';
        let currentCustomizationItem = null;
        let socket;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            loadProducts();
        });

        // 初始化 Socket.IO
        function initializeSocket() {
            socket = io('http://localhost:3001');

            socket.on('connect', function() {
                console.log('已連接到服務器');
            });

            socket.on('disconnect', function() {
                console.log('與服務器斷開連接');
            });
        }

        // 載入商品資料
        async function loadProducts() {
            try {
                const response = await fetch('http://localhost:3001/api/products');
                products = await response.json();
                renderCategories();
                if (products.length > 0) {
                    const firstCategory = [...new Set(products.map(p => p.category))][0];
                    showCategory(firstCategory);
                }
            } catch (error) {
                console.error('載入商品失敗:', error);
            }
        }

        // 渲染分類標籤
        function renderCategories() {
            const categories = [...new Set(products.map(p => p.category))];
            const categoryTabs = document.getElementById('categoryTabs');

            categoryTabs.innerHTML = categories.map(category =>
                `<button class="category-tab" onclick="showCategory('${category}')">${category}</button>`
            ).join('');
        }

        // 顯示分類商品
        function showCategory(category) {
            currentCategory = category;

            // 更新分類標籤樣式
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
                if (tab.textContent === category) {
                    tab.classList.add('active');
                }
            });

            // 渲染商品
            const categoryProducts = products.filter(p => p.category === category);
            const menuGrid = document.getElementById('menuGrid');

            menuGrid.innerHTML = categoryProducts.map(product => `
                <div class="menu-item" onclick="showCustomizationModal(${product.id})">
                    <h3>${product.name}</h3>
                    <div class="price">$${product.price}</div>
                </div>
            `).join('');
        }

        // 顯示客製化選項彈窗
        function showCustomizationModal(productId) {
            const product = products.find(p => p.id === productId);
            currentCustomizationItem = product;

            const modal = document.getElementById('customizationModal');
            const options = document.getElementById('customizationOptions');

            // 根據商品類型生成客製化選項
            let customizationHTML = '';

            if (product.category === '漢堡' || product.category === '蛋餅' || product.category === '吐司') {
                customizationHTML = `
                    <div class="customization-option">
                        <input type="checkbox" id="no-onion" value="不加洋蔥">
                        <label for="no-onion">不加洋蔥</label>
                    </div>
                    <div class="customization-option">
                        <input type="checkbox" id="no-sauce" value="不加醬料">
                        <label for="no-sauce">不加醬料</label>
                    </div>
                    <div class="customization-option">
                        <input type="checkbox" id="add-egg" value="加蛋 +10元">
                        <label for="add-egg">加蛋 (+$10)</label>
                    </div>
                    <div class="customization-option">
                        <input type="checkbox" id="spicy" value="要辣">
                        <label for="spicy">要辣</label>
                    </div>
                `;
            } else if (product.category === '飲料') {
                customizationHTML = `
                    <div class="customization-option">
                        <input type="radio" id="ice-normal" name="ice" value="正常冰" checked>
                        <label for="ice-normal">正常冰</label>
                    </div>
                    <div class="customization-option">
                        <input type="radio" id="ice-less" name="ice" value="少冰">
                        <label for="ice-less">少冰</label>
                    </div>
                    <div class="customization-option">
                        <input type="radio" id="ice-none" name="ice" value="去冰">
                        <label for="ice-none">去冰</label>
                    </div>
                    <div class="customization-option">
                        <input type="radio" id="sugar-normal" name="sugar" value="正常糖" checked>
                        <label for="sugar-normal">正常糖</label>
                    </div>
                    <div class="customization-option">
                        <input type="radio" id="sugar-less" name="sugar" value="少糖">
                        <label for="sugar-less">少糖</label>
                    </div>
                    <div class="customization-option">
                        <input type="radio" id="sugar-none" name="sugar" value="無糖">
                        <label for="sugar-none">無糖</label>
                    </div>
                `;
            }

            options.innerHTML = customizationHTML;
            modal.style.display = 'block';
        }

        // 確認客製化選項
        function confirmCustomization() {
            const customizations = [];
            let additionalPrice = 0;

            // 收集選中的客製化選項
            document.querySelectorAll('#customizationOptions input:checked').forEach(input => {
                customizations.push(input.value);
                if (input.value.includes('+10元')) {
                    additionalPrice += 10;
                }
            });

            // 添加到訂單
            addToOrder(currentCustomizationItem, customizations, additionalPrice);

            closeCustomizationModal();
        }

        // 關閉客製化選項彈窗
        function closeCustomizationModal() {
            document.getElementById('customizationModal').style.display = 'none';
            currentCustomizationItem = null;
        }

        // 添加到訂單
        function addToOrder(product, customizations = [], additionalPrice = 0) {
            const existingItem = currentOrder.find(item =>
                item.id === product.id &&
                JSON.stringify(item.customizations) === JSON.stringify(customizations)
            );

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                currentOrder.push({
                    id: product.id,
                    name: product.name,
                    price: product.price + additionalPrice,
                    quantity: 1,
                    customizations: customizations
                });
            }

            renderOrder();
        }

        // 渲染訂單
        function renderOrder() {
            const orderItems = document.getElementById('orderItems');

            if (currentOrder.length === 0) {
                orderItems.innerHTML = '<div class="empty-cart">尚無點餐項目</div>';
            } else {
                orderItems.innerHTML = currentOrder.map((item, index) => `
                    <div class="order-item">
                        <div class="order-item-info">
                            <div class="order-item-name">${item.name}</div>
                            <div class="order-item-customizations">
                                ${item.customizations.map(c => `• ${c}`).join('<br>')}
                            </div>
                        </div>
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="changeQuantity(${index}, -1)">-</button>
                            <span>${item.quantity}</span>
                            <button class="quantity-btn" onclick="changeQuantity(${index}, 1)">+</button>
                            <button class="remove-btn" onclick="removeFromOrder(${index})">移除</button>
                        </div>
                    </div>
                `).join('');
            }

            updateTotal();
        }

        // 修改數量
        function changeQuantity(index, change) {
            currentOrder[index].quantity += change;
            if (currentOrder[index].quantity <= 0) {
                currentOrder.splice(index, 1);
            }
            renderOrder();
        }

        // 從訂單中移除項目
        function removeFromOrder(index) {
            currentOrder.splice(index, 1);
            renderOrder();
        }

        // 更新總計
        function updateTotal() {
            const total = currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            document.getElementById('orderTotal').textContent = `總計: $${total}`;
        }

        // 清除訂單
        function clearOrder() {
            if (currentOrder.length > 0 && confirm('確定要清除所有訂單項目嗎？')) {
                currentOrder = [];
                renderOrder();
            }
        }

        // 送單至廚房
        async function sendOrder() {
            if (currentOrder.length === 0) {
                alert('請先選擇餐點');
                return;
            }

            const totalAmount = currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);

            try {
                const response = await fetch('http://localhost:3001/api/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        items: currentOrder,
                        totalAmount: totalAmount
                    })
                });

                if (response.ok) {
                    const order = await response.json();
                    alert(`訂單已送出！訂單編號: ${order.order_number}`);
                    currentOrder = [];
                    renderOrder();
                } else {
                    alert('送單失敗，請重試');
                }
            } catch (error) {
                console.error('送單失敗:', error);
                alert('送單失敗，請重試');
            }
        }

                // 顯示結帳彈窗
        function showCheckoutModal() {
            if (currentOrder.length === 0) {
                alert('請先選擇餐點');
                return;
            }

            const total = currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            document.getElementById('checkoutAmount').textContent = total;
            document.getElementById('receivedAmount').value = '';
            document.getElementById('changeAmount').textContent = '0';
            document.getElementById('checkoutModal').style.display = 'block';
        }

        // 關閉結帳彈窗
        function closeCheckoutModal() {
            document.getElementById('checkoutModal').style.display = 'none';
        }

        // 計算找零
        function calculateChange() {
            const total = parseInt(document.getElementById('checkoutAmount').textContent);
            const received = parseInt(document.getElementById('receivedAmount').value) || 0;
            const change = received - total;
            document.getElementById('changeAmount').textContent = change >= 0 ? change : 0;
        }

        // 完成結帳
        async function completeCheckout() {
            const total = parseInt(document.getElementById('checkoutAmount').textContent);
            const received = parseInt(document.getElementById('receivedAmount').value) || 0;

            if (received < total) {
                alert('實收金額不足');
                return;
            }

            // 先送單至廚房
            try {
                const response = await fetch('http://localhost:3001/api/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        items: currentOrder,
                        totalAmount: total
                    })
                });

                if (response.ok) {
                    const order = await response.json();
                    const change = received - total;
                    alert(`結帳完成！\n訂單編號: ${order.order_number}\n找零: $${change}`);
                    currentOrder = [];
                    renderOrder();
                    closeCheckoutModal();
                } else {
                    alert('結帳失敗，請重試');
                }
            } catch (error) {
                console.error('結帳失敗:', error);
                alert('結帳失敗，請重試');
            }
        }

        // 點擊彈窗外部關閉彈窗
        window.onclick = function(event) {
            const customizationModal = document.getElementById('customizationModal');
            const checkoutModal = document.getElementById('checkoutModal');

            if (event.target === customizationModal) {
                closeCustomizationModal();
            }
            if (event.target === checkoutModal) {
                closeCheckoutModal();
            }
        }
    </script>
</body>
</html>