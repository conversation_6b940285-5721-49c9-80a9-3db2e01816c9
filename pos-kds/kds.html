<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>廚房顯示系統 - KDS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #2c3e50;
            color: white;
            height: 100vh;
            overflow: hidden;
        }

        .kds-header {
            background: #34495e;
            padding: 20px;
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            border-bottom: 3px solid #3498db;
        }

        .orders-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
            height: calc(100vh - 80px);
            overflow-y: auto;
        }

        .order-card {
            background: #34495e;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 20px;
            position: relative;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .order-card.urgent {
            border-color: #e74c3c;
            background: #4a3842;
        }

        .order-card.very-urgent {
            border-color: #e74c3c;
            background: #5d2e2e;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
            100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #5a6c7d;
        }

        .order-number {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }

        .order-time {
            font-size: 14px;
            color: #bdc3c7;
        }

        .order-time.urgent {
            color: #f39c12;
            font-weight: bold;
        }

        .order-time.very-urgent {
            color: #e74c3c;
            font-weight: bold;
        }

        .order-items {
            margin-bottom: 20px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #485563;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-name {
            font-size: 16px;
            font-weight: bold;
        }

        .item-quantity {
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 14px;
        }

        .item-customizations {
            font-size: 12px;
            color: #f39c12;
            margin-top: 5px;
            font-style: italic;
        }

        .complete-btn {
            width: 100%;
            padding: 15px;
            background: #27ae60;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .complete-btn:hover {
            background: #229954;
        }

        .complete-btn:active {
            background: #1e8449;
        }

        .empty-orders {
            grid-column: 1 / -1;
            text-align: center;
            font-size: 24px;
            color: #7f8c8d;
            margin-top: 100px;
        }

        .stats-bar {
            background: #34495e;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            border-top: 1px solid #5a6c7d;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stat-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .stat-indicator.normal {
            background: #3498db;
        }

        .stat-indicator.urgent {
            background: #f39c12;
        }

        .stat-indicator.very-urgent {
            background: #e74c3c;
        }

        .sound-toggle {
            background: #7f8c8d;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .sound-toggle.on {
            background: #27ae60;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            z-index: 1000;
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="kds-header">
        廚房顯示系統 (KDS)
    </div>

    <div class="orders-container" id="ordersContainer">
        <div class="empty-orders">
            目前沒有待處理的訂單
        </div>
    </div>

    <div class="stats-bar">
        <div class="stat-item">
            <div class="stat-indicator normal"></div>
            <span>正常 (0-5分鐘)</span>
        </div>
        <div class="stat-item">
            <div class="stat-indicator urgent"></div>
            <span>注意 (5-10分鐘)</span>
        </div>
        <div class="stat-item">
            <div class="stat-indicator very-urgent"></div>
            <span>緊急 (超過10分鐘)</span>
        </div>
        <div class="stat-item">
            總訂單數: <span id="totalOrders">0</span>
        </div>
        <button class="sound-toggle" id="soundToggle" onclick="toggleSound()">
            提示音: 開啟
        </button>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let orders = [];
        let socket;
        let soundEnabled = true;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            loadPendingOrders();
            startTimeUpdater();
        });

        // 初始化 Socket.IO
        function initializeSocket() {
            socket = io('http://localhost:3001');
            
            socket.on('connect', function() {
                console.log('KDS 已連接到服務器');
            });

            socket.on('new_order', function(order) {
                console.log('收到新訂單:', order);
                orders.push(order);
                renderOrders();
                playNotificationSound();
                showNotification(`新訂單 #${order.order_number}`);
            });

            socket.on('order_completed', function(data) {
                console.log('訂單已完成:', data);
                orders = orders.filter(order => order.id !== data.orderId);
                renderOrders();
            });

            socket.on('disconnect', function() {
                console.log('KDS 與服務器斷開連接');
            });
        }

        // 載入待處理訂單
        async function loadPendingOrders() {
            try {
                const response = await fetch('http://localhost:3001/api/orders/pending');
                orders = await response.json();
                renderOrders();
            } catch (error) {
                console.error('載入訂單失敗:', error);
            }
        }

        // 渲染訂單
        function renderOrders() {
            const container = document.getElementById('ordersContainer');
            
            if (orders.length === 0) {
                container.innerHTML = '<div class="empty-orders">目前沒有待處理的訂單</div>';
            } else {
                container.innerHTML = orders.map(order => {
                    const waitTime = getWaitTime(order.created_at);
                    const urgencyClass = getUrgencyClass(waitTime);
                    
                    return `
                        <div class="order-card ${urgencyClass}">
                            <div class="order-header">
                                <div class="order-number">#${order.order_number}</div>
                                <div class="order-time ${urgencyClass}">${formatWaitTime(waitTime)}</div>
                            </div>
                            <div class="order-items">
                                ${order.items.map(item => `
                                    <div class="order-item">
                                        <div>
                                            <div class="item-name">${item.product_name}</div>
                                            ${item.customizations && JSON.parse(item.customizations).length > 0 ? 
                                                `<div class="item-customizations">
                                                    ${JSON.parse(item.customizations).map(c => `• ${c}`).join('<br>')}
                                                </div>` : ''
                                            }
                                        </div>
                                        <div class="item-quantity">x${item.quantity}</div>
                                    </div>
                                `).join('')}
                            </div>
                            <button class="complete-btn" onclick="completeOrder(${order.id})">
                                完成訂單
                            </button>
                        </div>
                    `;
                }).join('');
            }
            
            // 更新統計
            document.getElementById('totalOrders').textContent = orders.length;
        }

        // 計算等待時間（分鐘）
        function getWaitTime(createdAt) {
            const now = new Date();
            const created = new Date(createdAt);
            return Math.floor((now - created) / (1000 * 60));
        }

        // 獲取緊急程度樣式
        function getUrgencyClass(waitTime) {
            if (waitTime >= 10) return 'very-urgent';
            if (waitTime >= 5) return 'urgent';
            return '';
        }

        // 格式化等待時間
        function formatWaitTime(minutes) {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            
            if (hours > 0) {
                return `等候 ${hours}:${mins.toString().padStart(2, '0')}`;
            } else {
                return `等候 ${mins}分鐘`;
            }
        }

        // 完成訂單
        async function completeOrder(orderId) {
            try {
                const response = await fetch(`http://localhost:3001/api/orders/${orderId}/complete`, {
                    method: 'PUT'
                });
                
                if (response.ok) {
                                        orders = orders.filter(order => order.id !== orderId);
                    renderOrders();
                    showNotification('訂單已完成');
                } else {
                    alert('完成訂單失敗，請重試');
                }
            } catch (error) {
                console.error('完成訂單失敗:', error);
                alert('完成訂單失敗，請重試');
            }
        }

        // 開始時間更新器
        function startTimeUpdater() {
            setInterval(() => {
                renderOrders();
            }, 60000); // 每分鐘更新一次
        }

        // 播放提示音
        function playNotificationSound() {
            if (soundEnabled) {
                // 創建音頻提示
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
            }
        }

        // 切換提示音
        function toggleSound() {
            soundEnabled = !soundEnabled;
            const button = document.getElementById('soundToggle');
            button.textContent = soundEnabled ? '提示音: 開啟' : '提示音: 關閉';
            button.classList.toggle('on', soundEnabled);
        }

        // 顯示通知
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 初始化提示音按鈕狀態
        document.getElementById('soundToggle').classList.add('on');
    </script>
</body>
</html>