<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美味早餐店 - KDS 廚房顯示系統</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }

        .kds-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            padding: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 4px solid #3498db;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header-left h1 {
            font-size: 2.2em;
            margin-bottom: 5px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-left .subtitle {
            font-size: 1em;
            opacity: 0.8;
        }

        .header-right {
            text-align: right;
        }

        .datetime {
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .stats {
            display: flex;
            gap: 20px;
            font-size: 0.9em;
        }

        .stat-item {
            background: rgba(52, 152, 219, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }

        .orders-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            padding: 25px;
            height: calc(100vh - 120px);
            overflow-y: auto;
        }

        .order-card {
            background: linear-gradient(145deg, #34495e, #2c3e50);
            border: 2px solid #3498db;
            border-radius: 15px;
            padding: 25px;
            position: relative;
            animation: slideIn 0.5s ease-out;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .order-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.4);
        }

        @keyframes slideIn {
            from {
                transform: translateX(-100%) scale(0.8);
                opacity: 0;
            }
            to {
                transform: translateX(0) scale(1);
                opacity: 1;
            }
        }

        .order-card.urgent {
            border-color: #f39c12;
            background: linear-gradient(145deg, #4a3c2a, #3e2723);
            animation: urgentPulse 3s infinite;
        }

        .order-card.very-urgent {
            border-color: #e74c3c;
            background: linear-gradient(145deg, #5d2e2e, #4a1f1f);
            animation: veryUrgentPulse 2s infinite;
        }

        @keyframes urgentPulse {
            0%, 100% { 
                box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 0 0 rgba(243, 156, 18, 0.7); 
            }
            50% { 
                box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 0 10px rgba(243, 156, 18, 0); 
            }
        }

        @keyframes veryUrgentPulse {
            0%, 100% { 
                box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 0 0 rgba(231, 76, 60, 0.7); 
            }
            50% { 
                box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 0 15px rgba(231, 76, 60, 0); 
            }
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(52, 152, 219, 0.3);
        }

        .order-number {
            font-size: 2em;
            font-weight: 700;
            color: #3498db;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .order-time {
            text-align: right;
        }

        .time-elapsed {
            font-size: 1.4em;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .time-elapsed.urgent {
            color: #f39c12;
        }

        .time-elapsed.very-urgent {
            color: #e74c3c;
        }

        .created-time {
            font-size: 0.9em;
            opacity: 0.7;
        }

        .order-items {
            margin-bottom: 25px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-info {
            flex: 1;
        }

        .item-name {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-quantity {
            background: #3498db;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 600;
            margin-right: 15px;
        }

        .item-customizations {
            margin-top: 8px;
        }

        .customization-tag {
            display: inline-block;
            background: #e67e22;
            color: white;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-right: 8px;
            margin-bottom: 4px;
        }

        .order-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 2px solid rgba(52, 152, 219, 0.3);
        }

        .order-total {
            font-size: 1.2em;
            font-weight: 600;
            color: #2ecc71;
        }

        .complete-btn {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .complete-btn:hover {
            background: linear-gradient(45deg, #229954, #27ae60);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }

        .complete-btn:active {
            transform: translateY(0);
        }

        .empty-state {
            grid-column: 1 / -1;
            text-align: center;
            padding: 80px 20px;
            color: rgba(255,255,255,0.6);
        }

        .empty-state .icon {
            font-size: 5em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h2 {
            font-size: 1.8em;
            margin-bottom: 10px;
            opacity: 0.7;
        }

        .empty-state p {
            font-size: 1.1em;
            opacity: 0.5;
        }

        /* 聲音控制 */
        .sound-control {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(52, 152, 219, 0.2);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 50px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .sound-control:hover {
            background: rgba(52, 152, 219, 0.3);
        }

        .sound-control.muted {
            background: rgba(231, 76, 60, 0.2);
            border-color: rgba(231, 76, 60, 0.3);
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .kds-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .stats {
                justify-content: center;
            }
            
            .orders-container {
                grid-template-columns: 1fr;
                padding: 15px;
            }
            
            .order-card {
                padding: 20px;
            }
        }

        /* 載入動畫 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="kds-header">
        <div class="header-left">
            <h1>🍽️ 廚房顯示系統</h1>
            <div class="subtitle">Kitchen Display System</div>
        </div>
        <div class="header-right">
            <div class="datetime" id="datetime"></div>
            <div class="stats">
                <div class="stat-item">
                    待處理: <span id="pendingCount">0</span>
                </div>
                <div class="stat-item">
                    今日完成: <span id="completedCount">0</span>
                </div>
            </div>
        </div>
    </div>

    <div class="sound-control" id="soundControl" title="點擊切換聲音">
        🔊 聲音開啟
    </div>

    <div class="orders-container" id="ordersContainer">
        <div class="empty-state">
            <div class="icon">🍳</div>
            <h2>暫無待處理訂單</h2>
            <p>新訂單將會自動顯示在這裡</p>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        // 全域變數
        let socket;
        let orders = [];
        let soundEnabled = true;
        let completedToday = 0;

        // 初始化 KDS 系統
        function initializeKDS() {
            console.log('🚀 初始化 KDS 系統...');
            
            // 建立 Socket.IO 連接
            socket = io();
            
            socket.on('connect', function() {
                console.log('✅ KDS 系統連接成功');
                socket.emit('kds_ready');
            });
            
            socket.on('disconnect', function() {
                console.log('❌ KDS 系統連接中斷');
            });
            
            // 監聽新訂單
            socket.on('new_order', function(order) {
                console.log('📝 收到新訂單:', order);
                addOrder(order);
                playNotificationSound();
            });
            
            // 監聽訂單完成
            socket.on('order_completed', function(data) {
                console.log('✅ 訂單已完成:', data);
                removeOrder(data.orderId);
                completedToday++;
                updateStats();
            });
            
            // 監聽待處理訂單
            socket.on('pending_orders', function(pendingOrders) {
                console.log('📋 載入待處理訂單:', pendingOrders);
                orders = pendingOrders;
                renderOrders();
                updateStats();
            });
            
            // 載入今日統計
            loadTodayStats();
            
            // 更新時間顯示
            updateDateTime();
            setInterval(updateDateTime, 1000);
            
            // 更新等待時間
            setInterval(updateWaitTimes, 30000); // 每30秒更新一次
            
            // 綁定事件
            bindEvents();
        }

        // 載入今日統計
        async function loadTodayStats() {
            try {
                const response = await fetch('/api/orders/stats');
                const result = await response.json();
                
                if (result.success) {
                    completedToday = result.data.completed || 0;
                    updateStats();
                }
            } catch (error) {
                console.error('載入統計失敗:', error);
            }
        }

        // 添加訂單
        function addOrder(order) {
            // 檢查是否已存在
            const existingIndex = orders.findIndex(o => o.id === order.id);
            if (existingIndex === -1) {
                orders.unshift(order); // 新訂單加到最前面
                renderOrders();
                updateStats();
            }
        }

        // 移除訂單
        function removeOrder(orderId) {
            orders = orders.filter(order => order.id !== orderId);
            renderOrders();
            updateStats();
        }

        // 渲染訂單
        function renderOrders() {
            const container = document.getElementById('ordersContainer');
            
            if (orders.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">🍳</div>
                        <h2>暫無待處理訂單</h2>
                        <p>新訂單將會自動顯示在這裡</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = orders.map(order => {
                const waitTime = calculateWaitTime(order.created_at);
                const isUrgent = waitTime >= 10;
                const isVeryUrgent = waitTime >= 15;
                
                let cardClass = 'order-card';
                if (isVeryUrgent) cardClass += ' very-urgent';
                else if (isUrgent) cardClass += ' urgent';
                
                let timeClass = 'time-elapsed';
                if (isVeryUrgent) timeClass += ' very-urgent';
                else if (isUrgent) timeClass += ' urgent';
                
                return `
                    <div class="${cardClass}">
                        <div class="order-header">
                            <div class="order-number">#${order.order_number}</div>
                            <div class="order-time">
                                <div class="${timeClass}">${waitTime}分鐘</div>
                                <div class="created-time">${formatTime(order.created_at)}</div>
                            </div>
                        </div>
                        
                        <div class="order-items">
                            ${order.items.map(item => `
                                <div class="order-item">
                                    <div class="item-info">
                                        <div class="item-name">
                                            <span class="item-quantity">×${item.quantity}</span>
                                            ${item.product_name}
                                        </div>
                                        ${item.customizations && JSON.parse(item.customizations).length > 0 ? `
                                            <div class="item-customizations">
                                                ${JSON.parse(item.customizations).map(custom => 
                                                    `<span class="customization-tag">${custom}</span>`
                                                ).join('')}
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        
                        <div class="order-footer">
                            <div class="order-total">總計: $${order.total_amount}</div>
                            <button class="complete-btn" onclick="completeOrder(${order.id})">
                                完成訂單
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 計算等待時間
        function calculateWaitTime(createdAt) {
            const now = new Date();
            const created = new Date(createdAt);
            return Math.floor((now - created) / (1000 * 60)); // 分鐘
        }

        // 格式化時間
        function formatTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 完成訂單
        async function completeOrder(orderId) {
            try {
                const response = await fetch(`/api/orders/${orderId}/complete`, {
                    method: 'PUT'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    console.log('✅ 訂單完成成功');
                    // 訂單會通過 socket 事件自動移除
                } else {
                    alert('完成訂單失敗: ' + result.error);
                }
            } catch (error) {
                console.error('完成訂單失敗:', error);
                alert('完成訂單失敗，請稍後再試');
            }
        }

        // 更新等待時間
        function updateWaitTimes() {
            renderOrders();
        }

        // 更新統計
        function updateStats() {
            document.getElementById('pendingCount').textContent = orders.length;
            document.getElementById('completedCount').textContent = completedToday;
        }

        // 更新時間顯示
        function updateDateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('datetime').textContent = timeString;
        }

        // 播放通知聲音
        function playNotificationSound() {
            if (!soundEnabled) return;
            
            // 創建音頻上下文
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 創建振盪器
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        }

        // 切換聲音
        function toggleSound() {
            soundEnabled = !soundEnabled;
            const soundControl = document.getElementById('soundControl');
            
            if (soundEnabled) {
                soundControl.textContent = '🔊 聲音開啟';
                soundControl.classList.remove('muted');
                soundControl.title = '點擊關閉聲音';
            } else {
                soundControl.textContent = '🔇 聲音關閉';
                soundControl.classList.add('muted');
                soundControl.title = '點擊開啟聲音';
            }
        }

        // 綁定事件
        function bindEvents() {
            document.getElementById('soundControl').onclick = toggleSound;
        }

        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', initializeKDS);
    </script>
</body>
</html>
